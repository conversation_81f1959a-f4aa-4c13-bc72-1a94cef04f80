"use client";

import React, { useState, useCallback, useMemo } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useMobileLayout } from '@/hooks/use-mobile-layout';
import { MobileFormWrapper } from '@/components/mobile/MobileFormWrapper';
import { hapticFeedback } from './MobileGestures';

// Icons
import { 
  ArrowLeft,
  Check,
  Store,
  Package,
  Truck,
  User,
  Phone,
  MapPin,
  StickyNote,
  CreditCard,
  Receipt
} from 'lucide-react';

// Types
import type { OrderType } from '@/lib/types/order-types';
import type { TableLayout as Table } from '@/lib/db/table-db';
import type { OrderItem } from '@/lib/db/v4/schemas/order-schema';

interface MobileOrderFlowProps {
  items: OrderItem[];
  total: number;
  orderType: OrderType;
  tableId: string;
  tables: Table[];
  customer?: {
    name: string;
    phone: string;
    address?: string;
  };
  notes: string;
  onOrderTypeChange: (orderType: OrderType) => void;
  onTableChange: (tableId: string) => void;
  onCustomerChange: (customer: any) => void;
  onNotesChange: (notes: string) => void;
  onPlaceOrder: () => void;
  onCancel: () => void;
  isProcessing?: boolean;
}

interface OrderTypeOption {
  type: OrderType;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  color: string;
}

const orderTypeOptions: OrderTypeOption[] = [
  {
    type: 'dine-in',
    label: 'Sur Place',
    icon: Store,
    description: 'Service à table',
    color: '#22c55e'
  },
  {
    type: 'takeaway',
    label: 'Emporter',
    icon: Package,
    description: 'À emporter',
    color: '#f97316'
  },
  {
    type: 'delivery',
    label: 'Livraison',
    icon: Truck,
    description: 'Livraison à domicile',
    color: '#3b82f6'
  }
];

export const MobileOrderFlow: React.FC<MobileOrderFlowProps> = ({
  items,
  total,
  orderType,
  tableId,
  tables,
  customer,
  notes,
  onOrderTypeChange,
  onTableChange,
  onCustomerChange,
  onNotesChange,
  onPlaceOrder,
  onCancel,
  isProcessing = false
}) => {
  const { isMobile, safeAreaInsets } = useMobileLayout();
  const [currentStep, setCurrentStep] = useState<'type' | 'details' | 'summary'>('type');

  // Form state for customer info
  const [customerForm, setCustomerForm] = useState({
    name: customer?.name || '',
    phone: customer?.phone || '',
    address: customer?.address || ''
  });

  // Validation
  const isStepValid = useMemo(() => {
    switch (currentStep) {
      case 'type':
        return !!orderType;
      case 'details':
        if (orderType === 'dine-in') {
          return !!tableId;
        }
        if (orderType === 'takeaway') {
          return customerForm.name.trim() && customerForm.phone.trim();
        }
        if (orderType === 'delivery') {
          return customerForm.name.trim() && customerForm.phone.trim() && customerForm.address.trim();
        }
        return true;
      case 'summary':
        return items.length > 0;
      default:
        return false;
    }
  }, [currentStep, orderType, tableId, customerForm, items.length]);

  const canPlaceOrder = useMemo(() => {
    return items.length > 0 && 
           (orderType !== 'dine-in' || tableId) &&
           (orderType === 'dine-in' || (customerForm.name.trim() && customerForm.phone.trim())) &&
           (orderType !== 'delivery' || customerForm.address.trim()) &&
           !isProcessing;
  }, [items.length, orderType, tableId, customerForm, isProcessing]);

  // Handlers
  const handleNext = useCallback(() => {
    if (currentStep === 'type') {
      setCurrentStep('details');
    } else if (currentStep === 'details') {
      // Update customer info
      if (orderType !== 'dine-in') {
        onCustomerChange(customerForm);
      }
      setCurrentStep('summary');
    }
  }, [currentStep, orderType, customerForm, onCustomerChange]);

  const handleBack = useCallback(() => {
    if (currentStep === 'details') {
      setCurrentStep('type');
    } else if (currentStep === 'summary') {
      setCurrentStep('details');
    } else {
      onCancel();
    }
  }, [currentStep, onCancel]);

  const handlePlaceOrder = useCallback(() => {
    if (canPlaceOrder) {
      hapticFeedback.success();
      onPlaceOrder();
    }
  }, [canPlaceOrder, onPlaceOrder]);

  // Get current order type option
  const currentOrderTypeOption = orderTypeOptions.find(opt => opt.type === orderType);

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header */}
      <div 
        className="flex-shrink-0 bg-background border-b p-4"
        style={{ paddingTop: `max(${safeAreaInsets.top}px, 16px)` }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleBack}
              className="h-9 w-9"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="font-semibold text-lg">
                {currentStep === 'type' && 'Type de commande'}
                {currentStep === 'details' && 'Détails'}
                {currentStep === 'summary' && 'Confirmation'}
              </h1>
              <p className="text-sm text-muted-foreground">
                Étape {currentStep === 'type' ? '1' : currentStep === 'details' ? '2' : '3'} sur 3
              </p>
            </div>
          </div>
          
          <div className="text-right">
            <div className="font-bold text-lg">{total} DA</div>
            <div className="text-xs text-muted-foreground">{items.length} articles</div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-4">
            {/* Step 1: Order Type Selection */}
            {currentStep === 'type' && (
              <div className="space-y-4">
                <div className="text-center mb-6">
                  <h2 className="text-xl font-semibold mb-2">Comment souhaitez-vous être servi ?</h2>
                  <p className="text-muted-foreground">Choisissez le type de service</p>
                </div>
                
                <div className="space-y-3">
                  {orderTypeOptions.map((option) => {
                    const isSelected = orderType === option.type;
                    const Icon = option.icon;
                    
                    return (
                      <button
                        key={option.type}
                        onClick={() => {
                          hapticFeedback.light();
                          onOrderTypeChange(option.type);
                        }}
                        className={cn(
                          "w-full p-4 rounded-lg border-2 transition-all touch-manipulation",
                          isSelected 
                            ? "bg-primary/5 border-current" 
                            : "border-muted hover:border-muted-foreground/30"
                        )}
                        style={isSelected ? { borderColor: option.color } : {}}
                      >
                        <div className="flex items-center gap-4">
                          <div 
                            className="w-12 h-12 rounded-full flex items-center justify-center"
                            style={{ backgroundColor: `${option.color}20`, color: option.color }}
                          >
                            <Icon className="h-6 w-6" />
                          </div>
                          <div className="flex-1 text-left">
                            <div className="font-semibold text-base">{option.label}</div>
                            <div className="text-sm text-muted-foreground">{option.description}</div>
                          </div>
                          {isSelected && (
                            <div 
                              className="w-6 h-6 rounded-full flex items-center justify-center"
                              style={{ backgroundColor: option.color }}
                            >
                              <Check className="h-4 w-4 text-white" />
                            </div>
                          )}
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Step 2: Details */}
            {currentStep === 'details' && (
              <MobileFormWrapper>
                <div className="space-y-6">
                  {/* Order Type Display */}
                  {currentOrderTypeOption && (
                    <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                      <currentOrderTypeOption.icon 
                        className="h-5 w-5" 
                        style={{ color: currentOrderTypeOption.color }} 
                      />
                      <span className="font-medium">{currentOrderTypeOption.label}</span>
                    </div>
                  )}

                  {/* Table Selection for Dine-in */}
                  {orderType === 'dine-in' && (
                    <div className="space-y-3">
                      <h3 className="font-medium text-base">Sélectionner une table</h3>
                      <div className="grid grid-cols-3 gap-2">
                        {tables.map((table) => {
                          const isSelected = tableId === table.id;
                          const isOccupied = table.status === 'occupied';
                          
                          return (
                            <button
                              key={table.id}
                              onClick={() => !isOccupied && onTableChange(table.id)}
                              disabled={isOccupied}
                              className={cn(
                                "p-3 rounded-lg border-2 transition-all touch-manipulation",
                                isSelected 
                                  ? "border-primary bg-primary/5" 
                                  : isOccupied
                                    ? "border-muted bg-muted/50 opacity-50"
                                    : "border-muted hover:border-muted-foreground/30"
                              )}
                            >
                              <div className="text-center">
                                <div className="font-medium text-sm">Table {table.name}</div>
                                <div className="text-xs text-muted-foreground">
                                  {isOccupied ? 'Occupée' : 'Libre'}
                                </div>
                              </div>
                            </button>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  {/* Customer Info for Takeaway/Delivery */}
                  {(orderType === 'takeaway' || orderType === 'delivery') && (
                    <div className="space-y-4">
                      <h3 className="font-medium text-base">Informations client</h3>
                      
                      <div className="space-y-3">
                        <div className="relative">
                          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Nom du client"
                            value={customerForm.name}
                            onChange={(e) => setCustomerForm(prev => ({ ...prev, name: e.target.value }))}
                            className="pl-10 h-12"
                          />
                        </div>
                        
                        <div className="relative">
                          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Numéro de téléphone"
                            value={customerForm.phone}
                            onChange={(e) => setCustomerForm(prev => ({ ...prev, phone: e.target.value }))}
                            className="pl-10 h-12"
                            type="tel"
                          />
                        </div>
                        
                        {orderType === 'delivery' && (
                          <div className="relative">
                            <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                            <Textarea
                              placeholder="Adresse de livraison complète"
                              value={customerForm.address}
                              onChange={(e) => setCustomerForm(prev => ({ ...prev, address: e.target.value }))}
                              className="pl-10 min-h-[80px] resize-none"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Notes */}
                  <div className="space-y-3">
                    <h3 className="font-medium text-base flex items-center gap-2">
                      <StickyNote className="h-4 w-4" />
                      Notes spéciales
                    </h3>
                    <Textarea
                      placeholder="Instructions spéciales pour cette commande..."
                      value={notes}
                      onChange={(e) => onNotesChange(e.target.value)}
                      className="min-h-[80px] resize-none"
                    />
                  </div>
                </div>
              </MobileFormWrapper>
            )}

            {/* Step 3: Summary */}
            {currentStep === 'summary' && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h2 className="text-xl font-semibold mb-2">Confirmer la commande</h2>
                  <p className="text-muted-foreground">Vérifiez les détails avant de valider</p>
                </div>

                {/* Order Summary */}
                <div className="bg-card rounded-lg border p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">Résumé de la commande</h3>
                    <Badge variant="secondary">{items.length} articles</Badge>
                  </div>
                  
                  <div className="space-y-2">
                    {items.slice(0, 3).map((item, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span>{item.quantity}x {item.name}</span>
                        <span>{(item.price * item.quantity)} DA</span>
                      </div>
                    ))}
                    {items.length > 3 && (
                      <div className="text-sm text-muted-foreground">
                        ... et {items.length - 3} autres articles
                      </div>
                    )}
                  </div>
                  
                  <Separator />
                  
                  <div className="flex justify-between font-semibold">
                    <span>Total</span>
                    <span>{total} DA</span>
                  </div>
                </div>

                {/* Order Details */}
                <div className="bg-card rounded-lg border p-4 space-y-3">
                  <h3 className="font-medium">Détails de la commande</h3>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Type:</span>
                      <span>{currentOrderTypeOption?.label}</span>
                    </div>
                    
                    {orderType === 'dine-in' && tableId && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Table:</span>
                        <span>{tables.find(t => t.id === tableId)?.name}</span>
                      </div>
                    )}
                    
                    {(orderType === 'takeaway' || orderType === 'delivery') && customer && (
                      <>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Client:</span>
                          <span>{customer.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Téléphone:</span>
                          <span>{customer.phone}</span>
                        </div>
                        {orderType === 'delivery' && customer.address && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Adresse:</span>
                            <span className="text-right max-w-[60%]">{customer.address}</span>
                          </div>
                        )}
                      </>
                    )}
                    
                    {notes && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Notes:</span>
                        <span className="text-right max-w-[60%]">{notes}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {/* Bottom padding for safe area and buttons */}
          <div style={{ height: `${safeAreaInsets.bottom + 100}px` }} />
        </ScrollArea>
      </div>

      {/* Action Buttons */}
      <div 
        className="flex-shrink-0 bg-background border-t p-4"
        style={{ paddingBottom: `max(${safeAreaInsets.bottom}px, 16px)` }}
      >
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={handleBack}
            className="flex-1 h-12 text-base"
            disabled={isProcessing}
          >
            {currentStep === 'type' ? 'Annuler' : 'Retour'}
          </Button>
          
          {currentStep === 'summary' ? (
            <Button
              onClick={handlePlaceOrder}
              disabled={!canPlaceOrder}
              className="flex-1 h-12 text-base font-semibold"
            >
              {isProcessing ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Traitement...
                </>
              ) : (
                <>
                  <Receipt className="h-4 w-4 mr-2" />
                  Confirmer ({total} DA)
                </>
              )}
            </Button>
          ) : (
            <Button
              onClick={handleNext}
              disabled={!isStepValid}
              className="flex-1 h-12 text-base font-semibold"
            >
              Suivant
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
