"use client";

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useMobileLayout } from '@/hooks/use-mobile-layout';

// Icons
import { 
  Check, 
  Plus, 
  Minus,
  X,
  ArrowLeft,
  Pizza,
  StickyNote
} from 'lucide-react';

// Types
import type { MenuItem, Addon } from '@/lib/db/v4/schemas/menu-schema';
import type { OrderAddon, PizzaQuarter } from '@/lib/db/v4/schemas/order-schema';

interface MobileItemCustomizationProps {
  item: MenuItem;
  categoryId: string;
  categoryColor: string;
  onConfirm: (size: string, addons: OrderAddon[], notes: string) => void;
  onCancel: () => void;
  onCustomPizzaConfirm?: (quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average') => void;
  availablePizzas?: MenuItem[];
}

interface MobileSizeSelectorProps {
  item: MenuItem;
  selectedSize: string;
  onSizeChange: (size: string) => void;
  categoryColor: string;
}

interface MobileAddonSelectorProps {
  addons: Addon[];
  selectedAddons: Set<string>;
  onAddonToggle: (addonId: string) => void;
  categoryColor: string;
}

interface MobilePizzaBuilderProps {
  availablePizzas: MenuItem[];
  selectedSize: string;
  categoryColor: string;
  onConfirm: (quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average') => void;
  onCancel: () => void;
}

const MobileSizeSelector: React.FC<MobileSizeSelectorProps> = ({
  item,
  selectedSize,
  onSizeChange,
  categoryColor
}) => {
  const sizes = Object.keys(item.prices);
  
  if (sizes.length <= 1) return null;

  return (
    <div className="space-y-3">
      <h3 className="font-medium text-sm">Taille</h3>
      <div className="grid grid-cols-1 gap-2">
        {sizes.map((size) => {
          const isSelected = size === selectedSize;
          const price = item.prices[size];
          
          return (
            <button
              key={size}
              onClick={() => onSizeChange(size)}
              className={cn(
                "flex items-center justify-between p-3 rounded-lg border transition-all touch-manipulation",
                isSelected 
                  ? "border-2 bg-primary/5" 
                  : "border hover:border-muted-foreground/30"
              )}
              style={isSelected ? { borderColor: categoryColor } : {}}
            >
              <div className="flex items-center gap-3">
                <div 
                  className={cn(
                    "w-4 h-4 rounded-full border-2 flex items-center justify-center",
                    isSelected ? "border-current" : "border-muted-foreground/30"
                  )}
                  style={isSelected ? { borderColor: categoryColor, backgroundColor: categoryColor } : {}}
                >
                  {isSelected && <Check className="w-2 h-2 text-white" />}
                </div>
                <div>
                  <div className="font-medium text-sm text-left">
                    {size === 'default' ? 'Classique' : size}
                  </div>
                </div>
              </div>
              <div className="font-semibold text-sm">
                {price} DA
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
};

const MobileAddonSelector: React.FC<MobileAddonSelectorProps> = ({
  addons,
  selectedAddons,
  onAddonToggle,
  categoryColor
}) => {
  if (!addons || addons.length === 0) return null;

  return (
    <div className="space-y-3">
      <h3 className="font-medium text-sm">Options supplémentaires</h3>
      <div className="space-y-2">
        {addons.map((addon) => {
          const isSelected = selectedAddons.has(addon.id);
          
          return (
            <button
              key={addon.id}
              onClick={() => onAddonToggle(addon.id)}
              className={cn(
                "flex items-center justify-between p-3 rounded-lg border transition-all touch-manipulation w-full",
                isSelected 
                  ? "border-2 bg-primary/5" 
                  : "border hover:border-muted-foreground/30"
              )}
              style={isSelected ? { borderColor: categoryColor } : {}}
            >
              <div className="flex items-center gap-3">
                <div 
                  className={cn(
                    "w-4 h-4 rounded border-2 flex items-center justify-center",
                    isSelected ? "border-current" : "border-muted-foreground/30"
                  )}
                  style={isSelected ? { borderColor: categoryColor, backgroundColor: categoryColor } : {}}
                >
                  {isSelected && <Check className="w-2 h-2 text-white" />}
                </div>
                <div className="text-left">
                  <div className="font-medium text-sm">{addon.name}</div>
                  {addon.description && (
                    <div className="text-xs text-muted-foreground">{addon.description}</div>
                  )}
                </div>
              </div>
              <div className="font-semibold text-sm">
                {addon.price > 0 ? `+${addon.price} DA` : 'Gratuit'}
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export const MobileItemCustomization: React.FC<MobileItemCustomizationProps> = ({
  item,
  categoryId,
  categoryColor,
  onConfirm,
  onCancel,
  onCustomPizzaConfirm,
  availablePizzas
}) => {
  const { isMobile, safeAreaInsets } = useMobileLayout();
  
  // State
  const [selectedSize, setSelectedSize] = useState(() => {
    const sizes = Object.keys(item.prices);
    return sizes[0] || 'default';
  });
  const [selectedAddons, setSelectedAddons] = useState<Set<string>>(new Set());
  const [notes, setNotes] = useState('');
  const [showPizzaBuilder, setShowPizzaBuilder] = useState(false);

  // Calculate total price
  const totalPrice = useMemo(() => {
    let basePrice = item.prices[selectedSize] || 0;
    
    if (item.addons) {
      selectedAddons.forEach(addonId => {
        const addon = item.addons?.find(a => a.id === addonId);
        if (addon) {
          basePrice += addon.price || 0;
        }
      });
    }
    
    return basePrice;
  }, [item, selectedSize, selectedAddons]);

  // Handle addon toggle
  const handleAddonToggle = useCallback((addonId: string) => {
    setSelectedAddons(prev => {
      const newSet = new Set(prev);
      if (newSet.has(addonId)) {
        newSet.delete(addonId);
      } else {
        newSet.add(addonId);
      }
      return newSet;
    });
  }, []);

  // Handle confirm
  const handleConfirm = useCallback(() => {
    const addonObjects: OrderAddon[] = [];
    
    if (item.addons) {
      selectedAddons.forEach(addonId => {
        const addon = item.addons?.find(a => a.id === addonId);
        if (addon) {
          addonObjects.push({
            id: addon.id,
            name: addon.name,
            price: addon.price || 0
          });
        }
      });
    }
    
    onConfirm(selectedSize, addonObjects, notes);
  }, [selectedSize, selectedAddons, notes, item.addons, onConfirm]);

  // Check if this is a pizza item that can be customized
  const isPizzaItem = item.name.toLowerCase().includes('pizza') || item.categoryId === 'pizza';
  const canBuildCustomPizza = isPizzaItem && availablePizzas && availablePizzas.length > 0 && onCustomPizzaConfirm;

  if (showPizzaBuilder && canBuildCustomPizza) {
    return (
      <MobilePizzaBuilder
        availablePizzas={availablePizzas}
        selectedSize={selectedSize}
        categoryColor={categoryColor}
        onConfirm={onCustomPizzaConfirm}
        onCancel={() => setShowPizzaBuilder(false)}
      />
    );
  }

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header */}
      <div 
        className="flex-shrink-0 bg-background border-b p-4"
        style={{ paddingTop: `max(${safeAreaInsets.top}px, 16px)` }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={onCancel}
              className="h-9 w-9"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="font-semibold text-lg">{item.name}</h1>
              {item.description && (
                <p className="text-sm text-muted-foreground">{item.description}</p>
              )}
            </div>
          </div>
          
          <div className="text-right">
            <div className="font-bold text-lg">{totalPrice} DA</div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-4 space-y-6">
            {/* Size Selection */}
            <MobileSizeSelector
              item={item}
              selectedSize={selectedSize}
              onSizeChange={setSelectedSize}
              categoryColor={categoryColor}
            />

            {/* Addon Selection */}
            {item.addons && item.addons.length > 0 && (
              <>
                <Separator />
                <MobileAddonSelector
                  addons={item.addons}
                  selectedAddons={selectedAddons}
                  onAddonToggle={handleAddonToggle}
                  categoryColor={categoryColor}
                />
              </>
            )}

            {/* Custom Pizza Option */}
            {canBuildCustomPizza && (
              <>
                <Separator />
                <div className="space-y-3">
                  <h3 className="font-medium text-sm">Pizza personnalisée</h3>
                  <Button
                    variant="outline"
                    onClick={() => setShowPizzaBuilder(true)}
                    className="w-full h-12 text-sm"
                  >
                    <Pizza className="h-4 w-4 mr-2" />
                    Créer une pizza personnalisée
                  </Button>
                </div>
              </>
            )}

            {/* Notes */}
            <Separator />
            <div className="space-y-3">
              <h3 className="font-medium text-sm flex items-center gap-2">
                <StickyNote className="h-4 w-4" />
                Notes spéciales
              </h3>
              <Textarea
                placeholder="Instructions spéciales pour cet article..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="min-h-[80px] resize-none"
              />
            </div>
          </div>
          
          {/* Bottom padding for safe area and buttons */}
          <div style={{ height: `${safeAreaInsets.bottom + 100}px` }} />
        </ScrollArea>
      </div>

      {/* Action Buttons */}
      <div 
        className="flex-shrink-0 bg-background border-t p-4"
        style={{ paddingBottom: `max(${safeAreaInsets.bottom}px, 16px)` }}
      >
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={onCancel}
            className="flex-1 h-12 text-base"
          >
            Annuler
          </Button>
          <Button
            onClick={handleConfirm}
            className="flex-1 h-12 text-base font-semibold"
            style={{ backgroundColor: categoryColor }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Ajouter ({totalPrice} DA)
          </Button>
        </div>
      </div>
    </div>
  );
};

// Placeholder for MobilePizzaBuilder - will be implemented if needed
const MobilePizzaBuilder: React.FC<MobilePizzaBuilderProps> = ({
  availablePizzas,
  selectedSize,
  categoryColor,
  onConfirm,
  onCancel
}) => {
  return (
    <div className="flex flex-col h-full bg-background">
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <Pizza className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-medium mb-2">Pizza Builder</h3>
          <p className="text-muted-foreground mb-4">
            Fonctionnalité en cours de développement
          </p>
          <Button onClick={onCancel}>Retour</Button>
        </div>
      </div>
    </div>
  );
};
