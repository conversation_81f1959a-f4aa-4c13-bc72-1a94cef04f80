"use client";

import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { useMobileLayout } from '@/hooks/use-mobile-layout';
import { SwipeGesture, PullToRefresh, hapticFeedback, useLongPress } from './MobileGestures';

// Icons
import { 
  Plus, 
  Search, 
  Star,
  Clock,
  Flame,
  Pizza,
  Settings
} from 'lucide-react';

// Types
import type { MenuItem, Category } from '@/lib/db/v4/schemas/menu-schema';
import type { OrderAddon } from '@/lib/db/v4/schemas/order-schema';

interface MobileMenuBrowserProps {
  categories: Category[];
  onAddItem: (item: MenuItem, size: string, addons: OrderAddon[], notes: string, categoryId: string) => void;
  onItemCustomize: (item: MenuItem, categoryId: string) => void;
  selectedCategory: string;
  onCategoryChange: (categoryId: string) => void;
}

interface MobileMenuItemCardProps {
  item: MenuItem;
  categoryId: string;
  categoryColor: string;
  onQuickAdd: (item: MenuItem, size: string) => void;
  onCustomize: (item: MenuItem) => void;
}

const MobileMenuItemCard: React.FC<MobileMenuItemCardProps> = ({
  item,
  categoryId,
  categoryColor,
  onQuickAdd,
  onCustomize
}) => {
  const { isMobile } = useMobileLayout();
  
  // Get default size and price
  const defaultSize = Object.keys(item.prices)[0] || 'default';
  const defaultPrice = item.prices[defaultSize] || 0;
  
  // Check if item has multiple sizes or addons
  const hasMultipleSizes = Object.keys(item.prices).length > 1;
  const hasAddons = item.addons && item.addons.length > 0;
  const needsCustomization = hasMultipleSizes || hasAddons;

  const handleQuickAdd = useCallback(() => {
    hapticFeedback.light();
    if (needsCustomization) {
      onCustomize(item);
    } else {
      onQuickAdd(item, defaultSize);
    }
  }, [item, defaultSize, needsCustomization, onQuickAdd, onCustomize]);

  // Long press for quick customization
  const longPressProps = useLongPress(() => {
    onCustomize(item);
  }, 600);

  return (
    <div
      className="bg-card rounded-lg border shadow-sm overflow-hidden touch-manipulation"
      style={{ borderLeftColor: categoryColor, borderLeftWidth: '3px' }}
      {...longPressProps}
    >
      {/* Item Image Placeholder */}
      <div 
        className="h-24 bg-gradient-to-br from-muted/50 to-muted flex items-center justify-center"
        style={{ backgroundColor: `${categoryColor}10` }}
      >
        <div 
          className="w-8 h-8 rounded-full flex items-center justify-center"
          style={{ backgroundColor: categoryColor }}
        >
          <span className="text-white text-sm font-bold">
            {item.name.charAt(0).toUpperCase()}
          </span>
        </div>
      </div>

      {/* Item Content */}
      <div className="p-3">
        <div className="flex items-start justify-between gap-2 mb-2">
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-sm leading-tight truncate">
              {item.name}
            </h3>
            {item.description && (
              <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                {item.description}
              </p>
            )}
          </div>
          
          <div className="text-right flex-shrink-0">
            <div className="font-semibold text-sm">
              {defaultPrice} DA
            </div>
            {hasMultipleSizes && (
              <div className="text-xs text-muted-foreground">
                dès
              </div>
            )}
          </div>
        </div>

        {/* Item Features */}
        <div className="flex items-center gap-1 mb-3">
          {hasMultipleSizes && (
            <Badge variant="outline" className="text-xs h-5 px-1.5">
              Tailles
            </Badge>
          )}
          {hasAddons && (
            <Badge variant="outline" className="text-xs h-5 px-1.5">
              Options
            </Badge>
          )}
          {item.isPopular && (
            <Badge variant="secondary" className="text-xs h-5 px-1.5">
              <Star className="w-3 h-3 mr-1" />
              Populaire
            </Badge>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          {needsCustomization ? (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCustomize(item)}
                className="flex-1 h-9 text-xs"
              >
                <Settings className="w-3 h-3 mr-1" />
                Personnaliser
              </Button>
              <Button
                size="sm"
                onClick={handleQuickAdd}
                className="h-9 px-3"
                style={{ backgroundColor: categoryColor }}
              >
                <Plus className="w-4 h-4" />
              </Button>
            </>
          ) : (
            <Button
              size="sm"
              onClick={handleQuickAdd}
              className="flex-1 h-9 text-xs"
              style={{ backgroundColor: categoryColor }}
            >
              <Plus className="w-3 h-3 mr-1" />
              Ajouter
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export const MobileMenuBrowser: React.FC<MobileMenuBrowserProps> = ({
  categories,
  onAddItem,
  onItemCustomize,
  selectedCategory,
  onCategoryChange
}) => {
  const { isMobile, safeAreaInsets } = useMobileLayout();
  const [searchQuery, setSearchQuery] = useState('');
  const categoryScrollRef = useRef<HTMLDivElement>(null);

  // Set initial category
  useEffect(() => {
    if (!selectedCategory && categories.length > 0) {
      onCategoryChange(categories[0].id);
    }
  }, [categories, selectedCategory, onCategoryChange]);

  // Get current category
  const currentCategory = useMemo(() => {
    return categories.find(cat => cat.id === selectedCategory) || categories[0];
  }, [categories, selectedCategory]);

  // Filter items based on search
  const filteredItems = useMemo(() => {
    if (!currentCategory) return [];
    
    if (!searchQuery.trim()) {
      return currentCategory.items || [];
    }
    
    return (currentCategory.items || []).filter(item =>
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (item.description && item.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  }, [currentCategory, searchQuery]);

  // Handle quick add
  const handleQuickAdd = useCallback((item: MenuItem, size: string) => {
    onAddItem(item, size, [], '', currentCategory?.id || '');
  }, [onAddItem, currentCategory]);

  // Handle item customization
  const handleItemCustomize = useCallback((item: MenuItem) => {
    onItemCustomize(item, currentCategory?.id || '');
  }, [onItemCustomize, currentCategory]);

  // Handle category swipe navigation
  const handleSwipeLeft = useCallback(() => {
    const currentIndex = categories.findIndex(cat => cat.id === selectedCategory);
    if (currentIndex < categories.length - 1) {
      onCategoryChange(categories[currentIndex + 1].id);
    }
  }, [categories, selectedCategory, onCategoryChange]);

  const handleSwipeRight = useCallback(() => {
    const currentIndex = categories.findIndex(cat => cat.id === selectedCategory);
    if (currentIndex > 0) {
      onCategoryChange(categories[currentIndex - 1].id);
    }
  }, [categories, selectedCategory, onCategoryChange]);

  // Pull to refresh handler
  const handleRefresh = useCallback(async () => {
    // Simulate refresh delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    // In a real app, you might refresh the menu data here
  }, []);

  // Category colors (you can customize these)
  const getCategoryColor = useCallback((categoryId: string) => {
    const colors = [
      '#ef4444', '#f97316', '#eab308', '#22c55e', 
      '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899'
    ];
    const index = categories.findIndex(cat => cat.id === categoryId);
    return colors[index % colors.length];
  }, [categories]);

  if (!currentCategory) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="text-muted-foreground">Aucune catégorie disponible</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Search Bar */}
      <div className="flex-shrink-0 p-4 border-b">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher un article..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-10"
          />
        </div>
      </div>

      {/* Category Tabs */}
      <div className="flex-shrink-0 border-b">
        <ScrollArea className="w-full" orientation="horizontal">
          <div 
            ref={categoryScrollRef}
            className="flex gap-1 p-2"
            style={{ paddingLeft: `max(${safeAreaInsets.left}px, 8px)`, paddingRight: `max(${safeAreaInsets.right}px, 8px)` }}
          >
            {categories.map((category) => {
              const isSelected = category.id === selectedCategory;
              const categoryColor = getCategoryColor(category.id);
              
              return (
                <Button
                  key={category.id}
                  variant={isSelected ? "default" : "outline"}
                  size="sm"
                  onClick={() => onCategoryChange(category.id)}
                  className={cn(
                    "flex-shrink-0 h-9 px-4 text-xs font-medium transition-all",
                    isSelected && "shadow-sm"
                  )}
                  style={isSelected ? { 
                    backgroundColor: categoryColor,
                    borderColor: categoryColor,
                    color: 'white'
                  } : {
                    borderColor: categoryColor,
                    color: categoryColor
                  }}
                >
                  {category.name}
                  {category.items && category.items.length > 0 && (
                    <Badge 
                      variant="secondary" 
                      className="ml-2 h-4 px-1 text-xs"
                    >
                      {category.items.length}
                    </Badge>
                  )}
                </Button>
              );
            })}
          </div>
        </ScrollArea>
      </div>

      {/* Menu Items Grid */}
      <div className="flex-1 overflow-hidden">
        <SwipeGesture
          onSwipeLeft={handleSwipeLeft}
          onSwipeRight={handleSwipeRight}
          className="h-full"
        >
          <PullToRefresh onRefresh={handleRefresh} className="h-full">
            <ScrollArea className="h-full">
              <div className="p-4">
            {searchQuery && (
              <div className="mb-4">
                <div className="text-sm text-muted-foreground">
                  {filteredItems.length} résultat{filteredItems.length !== 1 ? 's' : ''} pour "{searchQuery}"
                </div>
              </div>
            )}
            
            {filteredItems.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <div className="w-16 h-16 rounded-full bg-muted/50 flex items-center justify-center mb-4">
                  <Search className="h-6 w-6 text-muted-foreground" />
                </div>
                <div className="text-muted-foreground">
                  {searchQuery ? 'Aucun article trouvé' : 'Aucun article dans cette catégorie'}
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {filteredItems.map((item) => (
                  <MobileMenuItemCard
                    key={item.id}
                    item={item}
                    categoryId={currentCategory.id}
                    categoryColor={getCategoryColor(currentCategory.id)}
                    onQuickAdd={handleQuickAdd}
                    onCustomize={handleItemCustomize}
                  />
                ))}
              </div>
            )}
          </div>

              {/* Bottom padding for safe area */}
              <div style={{ height: `${safeAreaInsets.bottom + 80}px` }} />
            </ScrollArea>
          </PullToRefresh>
        </SwipeGesture>
      </div>
    </div>
  );
};
