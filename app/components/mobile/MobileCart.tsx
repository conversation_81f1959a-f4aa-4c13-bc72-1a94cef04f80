"use client";

import React, { useMemo, useCallback } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useMobileLayout } from '@/hooks/use-mobile-layout';
import { hapticFeedback } from './MobileGestures';

// Icons
import { 
  Plus, 
  Minus, 
  Trash2, 
  Edit3,
  ShoppingCart,
  Receipt,
  ArrowRight
} from 'lucide-react';

// Types
import type { OrderItem, OrderAddon } from '@/lib/db/v4/schemas/order-schema';

interface MobileCartProps {
  items: OrderItem[];
  total: number;
  onIncrement: (itemId: string) => void;
  onDecrement: (itemId: string) => void;
  onRemove: (itemId: string) => void;
  onItemEdit: (itemId: string) => void;
  onCheckout: () => void;
}

interface MobileCartItemProps {
  item: OrderItem;
  onIncrement: (itemId: string) => void;
  onDecrement: (itemId: string) => void;
  onRemove: (itemId: string) => void;
  onEdit: (itemId: string) => void;
}

const MobileCartItem: React.FC<MobileCartItemProps> = ({
  item,
  onIncrement,
  onDecrement,
  onRemove,
  onEdit
}) => {
  const { isMobile } = useMobileLayout();
  
  // Calculate item total price including addons
  const itemTotalPrice = useMemo(() => {
    let basePrice = item.price * item.quantity;
    if (item.addons && item.addons.length > 0) {
      const addonPrice = item.addons.reduce((sum, addon) => sum + (addon.price || 0), 0);
      basePrice += addonPrice * item.quantity;
    }
    return basePrice;
  }, [item]);

  const handleDecrement = useCallback(() => {
    hapticFeedback.light();
    if (item.quantity > 1) {
      onDecrement(item.id);
    } else {
      hapticFeedback.medium(); // Stronger feedback for removal
      onRemove(item.id);
    }
  }, [item.quantity, item.id, onDecrement, onRemove]);

  return (
    <div className="bg-card rounded-lg border p-4 space-y-3">
      {/* Item Header */}
      <div className="flex items-start justify-between gap-3">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h3 className="font-medium text-sm leading-tight">
              {item.name}
            </h3>
            {item.size && item.size !== 'default' && (
              <Badge variant="outline" className="text-xs h-5 px-1.5">
                {item.size}
              </Badge>
            )}
          </div>
          
          {item.notes && (
            <p className="text-xs text-muted-foreground mt-1 italic">
              {item.notes}
            </p>
          )}
        </div>
        
        <div className="text-right flex-shrink-0">
          <div className="font-semibold text-sm">
            {itemTotalPrice} DA
          </div>
          <div className="text-xs text-muted-foreground">
            {item.price} DA × {item.quantity}
          </div>
        </div>
      </div>

      {/* Addons */}
      {item.addons && item.addons.length > 0 && (
        <div className="space-y-1">
          <div className="text-xs font-medium text-muted-foreground">Options:</div>
          <div className="flex flex-wrap gap-1">
            {item.addons.map((addon: OrderAddon) => (
              <div 
                key={addon.id}
                className="inline-flex items-center gap-1 px-2 py-1 rounded-md bg-muted text-xs"
              >
                <span>{addon.name}</span>
                {addon.price > 0 && (
                  <span className="font-medium">+{addon.price} DA</span>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={handleDecrement}
            className="h-9 w-9 touch-manipulation"
          >
            {item.quantity > 1 ? (
              <Minus className="h-4 w-4" />
            ) : (
              <Trash2 className="h-4 w-4 text-destructive" />
            )}
          </Button>
          
          <div className="w-12 h-9 rounded-md border bg-muted flex items-center justify-center font-medium text-sm">
            {item.quantity}
          </div>
          
          <Button
            variant="outline"
            size="icon"
            onClick={() => {
              hapticFeedback.light();
              onIncrement(item.id);
            }}
            className="h-9 w-9 touch-manipulation"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onEdit(item.id)}
          className="h-9 px-3 text-xs"
        >
          <Edit3 className="h-3 w-3 mr-1" />
          Modifier
        </Button>
      </div>
    </div>
  );
};

export const MobileCart: React.FC<MobileCartProps> = ({
  items,
  total,
  onIncrement,
  onDecrement,
  onRemove,
  onItemEdit,
  onCheckout
}) => {
  const { isMobile, safeAreaInsets } = useMobileLayout();

  // Calculate summary stats
  const totalItems = useMemo(() => {
    return items.reduce((sum, item) => sum + item.quantity, 0);
  }, [items]);

  // Group items by category for better organization
  const groupedItems = useMemo(() => {
    const groups: { [key: string]: OrderItem[] } = {};
    
    items.forEach(item => {
      const categoryId = item.categoryId || 'other';
      if (!groups[categoryId]) {
        groups[categoryId] = [];
      }
      groups[categoryId].push(item);
    });
    
    return groups;
  }, [items]);

  if (items.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-8">
        <div className="w-20 h-20 rounded-full bg-muted/50 flex items-center justify-center mb-6">
          <ShoppingCart className="h-8 w-8 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-medium mb-2">Panier vide</h3>
        <p className="text-muted-foreground text-sm mb-6">
          Ajoutez des articles depuis le menu pour commencer votre commande
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Cart Header */}
      <div className="flex-shrink-0 p-4 border-b">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold">Panier</h2>
            <p className="text-sm text-muted-foreground">
              {totalItems} article{totalItems > 1 ? 's' : ''}
            </p>
          </div>
          
          <div className="text-right">
            <div className="text-lg font-bold">{total} DA</div>
            <div className="text-xs text-muted-foreground">Total</div>
          </div>
        </div>
      </div>

      {/* Cart Items */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-4 space-y-4">
            {Object.entries(groupedItems).map(([categoryId, categoryItems]) => (
              <div key={categoryId} className="space-y-3">
                {/* Category items */}
                {categoryItems.map((item) => (
                  <MobileCartItem
                    key={item.id}
                    item={item}
                    onIncrement={onIncrement}
                    onDecrement={onDecrement}
                    onRemove={onRemove}
                    onEdit={onItemEdit}
                  />
                ))}
              </div>
            ))}
          </div>
          
          {/* Bottom padding for safe area and checkout button */}
          <div style={{ height: `${safeAreaInsets.bottom + 100}px` }} />
        </ScrollArea>
      </div>

      {/* Checkout Section */}
      <div 
        className="flex-shrink-0 bg-background border-t p-4"
        style={{ paddingBottom: `max(${safeAreaInsets.bottom}px, 16px)` }}
      >
        {/* Order Summary */}
        <div className="space-y-2 mb-4">
          <div className="flex justify-between text-sm">
            <span>Sous-total ({totalItems} articles)</span>
            <span>{total} DA</span>
          </div>
          
          <Separator />
          
          <div className="flex justify-between font-semibold">
            <span>Total</span>
            <span>{total} DA</span>
          </div>
        </div>

        {/* Checkout Button */}
        <Button
          onClick={() => {
            hapticFeedback.medium();
            onCheckout();
          }}
          size="lg"
          className="w-full h-12 text-base font-semibold touch-manipulation"
        >
          <Receipt className="h-4 w-4 mr-2" />
          Passer la commande
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
};
