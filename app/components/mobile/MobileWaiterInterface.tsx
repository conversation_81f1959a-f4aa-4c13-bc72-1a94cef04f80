"use client";

import React, { useState, useCallback, useReducer, useMemo, useEffect, memo } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useMenuV4 } from '@/lib/hooks/use-menu-v4';
import { useOrderV4 } from '@/lib/hooks/use-order-v4';
import { useTableDB } from '@/lib/hooks/useTableDB';
import { useMobileLayout } from '@/hooks/use-mobile-layout';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

// Mobile-specific components
import { MobileContainer } from './MobileContainer';
import { MobileBottomNav } from './MobileBottomNav';
import { MobileMenuBrowser } from './MobileMenuBrowser';
import { MobileCart } from './MobileCart';
import { MobileOrderFlow } from './MobileOrderFlow';
import { MobileItemCustomization } from './MobileItemCustomization';

// Icons
import { 
  ShoppingCart, 
  Menu as MenuIcon, 
  Receipt, 
  Settings,
  ArrowLeft,
  Check
} from 'lucide-react';

// Types
import type { MenuItem, Category } from '@/lib/db/v4/schemas/menu-schema';
import type { OrderItem, OrderAddon, PizzaQuarter } from '@/lib/db/v4/schemas/order-schema';
import type { OrderType } from '@/lib/types/order-types';
import type { TableLayout as Table } from '@/lib/db/table-db';
import type { NewOrder } from '@/lib/db/v4';

// Mobile-specific types
interface MobileOrderState {
  _id: string;
  id: string;
  type: "order_document";
  orderType: OrderType;
  status: "pending";
  tableId: string;
  items: OrderItem[];
  total: number;
  createdAt: string;
  updatedAt: string;
  schemaVersion: "v4.0";
  notes: string;
  customer?: {
    name: string;
    phone: string;
    address?: string;
  };
  deliveryPerson?: {
    id: string;
    name: string;
  };
}

interface MobileUiState {
  selectedCategory: string;
  selectedItemForAddons: string | null;
  selectedItemSizes: { [key: string]: string };
  itemNotes: { [key: string]: string };
  selectedAddons: { [key: string]: Set<string> };
  lastAddedItem: string | null;
  currentView: 'menu' | 'cart' | 'order-flow' | 'customization';
  isCartOpen: boolean;
  isOrderFlowOpen: boolean;
  isCustomizationOpen: boolean;
}

// Mobile order actions
type MobileOrderAction = 
  | { type: 'INITIALIZE_ORDER' }
  | { type: 'LOAD_ORDER', payload: any }
  | { type: 'ADD_ITEM', payload: { item: MenuItem, size: string, addons: OrderAddon[], notes: string, categoryId: string } }
  | { type: 'ADD_CUSTOM_PIZZA', payload: { quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average' } }
  | { type: 'UPDATE_ITEM', payload: { itemId: string, updates: Partial<OrderItem> } }
  | { type: 'INCREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'DECREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'REMOVE_ITEM', payload: { itemId: string } }
  | { type: 'SET_ORDER_TYPE', payload: { orderType: OrderType } }
  | { type: 'SET_TABLE', payload: { tableId: string } }
  | { type: 'SET_CUSTOMER', payload: { customer: any } }
  | { type: 'SET_NOTES', payload: { notes: string } }
  | { type: 'CLEAR_ORDER' };

// Initial states
const initialOrderState: MobileOrderState = {
  _id: "",
  id: "",
  type: "order_document",
  orderType: "dine-in" as OrderType,
  status: "pending",
  tableId: "",
  items: [],
  total: 0,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  schemaVersion: "v4.0",
  notes: ""
};

const initialUiState: MobileUiState = {
  selectedCategory: "",
  selectedItemForAddons: null,
  selectedItemSizes: {},
  itemNotes: {},
  selectedAddons: {},
  lastAddedItem: null,
  currentView: 'menu',
  isCartOpen: false,
  isOrderFlowOpen: false,
  isCustomizationOpen: false
};

// Calculate total helper
const calculateTotal = (items: OrderItem[]): number => {
  return items.reduce((total, item) => {
    let itemPrice = item.price;
    if (item.addons && item.addons.length > 0) {
      itemPrice += item.addons.reduce((sum, addon) => sum + (addon.price || 0), 0);
    }
    return total + (itemPrice * item.quantity);
  }, 0);
};

// Order reducer
const mobileOrderReducer = (state: MobileOrderState, action: MobileOrderAction): MobileOrderState => {
  switch (action.type) {
    case 'INITIALIZE_ORDER':
      return { ...initialOrderState };
      
    case 'LOAD_ORDER':
      return { ...action.payload };
      
    case 'ADD_ITEM': {
      const { item, size, addons, notes, categoryId } = action.payload;
      const price = item.prices[size] || Object.values(item.prices)[0] || 0;
      const uniqueId = `order_item_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: item.id,
        name: item.name,
        size: size,
        price: price,
        quantity: 1,
        addons: addons,
        notes: notes,
        categoryId: categoryId
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
    
    case 'INCREMENT_QUANTITY': {
      const updatedItems = state.items.map(item =>
        item.id === action.payload.itemId
          ? { ...item, quantity: item.quantity + 1 }
          : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
    
    case 'DECREMENT_QUANTITY': {
      const updatedItems = state.items.map(item =>
        item.id === action.payload.itemId && item.quantity > 1
          ? { ...item, quantity: item.quantity - 1 }
          : item
      ).filter(item => item.quantity > 0);
      
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
    
    case 'REMOVE_ITEM': {
      const updatedItems = state.items.filter(item => item.id !== action.payload.itemId);
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
    
    case 'SET_ORDER_TYPE':
      return {
        ...state,
        orderType: action.payload.orderType
      };
      
    case 'SET_TABLE':
      return {
        ...state,
        tableId: action.payload.tableId
      };

    case 'SET_CUSTOMER':
      return {
        ...state,
        customer: action.payload.customer
      };

    case 'SET_NOTES':
      return {
        ...state,
        notes: action.payload.notes
      };

    case 'CLEAR_ORDER':
      return { ...initialOrderState };

    default:
      return state;
  }
};

const MobileWaiterInterface = memo(function MobileWaiterInterface() {
  // Hooks
  const { user } = useAuth();
  const { categories, isLoading: menuLoading, error: menuError, isReady: menuReady } = useMenuV4();
  const { createOrder, isLoading: ordersLoading, error: ordersError, isReady: ordersReady } = useOrderV4();
  const { tables, isLoading: tablesLoading, error: tablesError, isReady: tablesReady } = useTableDB();
  const { isMobile, safeAreaInsets } = useMobileLayout();
  const { toast } = useToast();

  // State
  const [orderState, dispatch] = useReducer(mobileOrderReducer, initialOrderState);
  const [uiState, setUiState] = useState<MobileUiState>(initialUiState);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedItemForCustomization, setSelectedItemForCustomization] = useState<{
    item: MenuItem;
    categoryId: string;
  } | null>(null);

  // Computed values
  const totalItemCount = useMemo(() => {
    return orderState.items.reduce((total, item) => total + item.quantity, 0);
  }, [orderState.items]);

  const canPlaceOrder = useMemo(() => {
    return orderState.items.length > 0 && 
           (orderState.orderType !== 'dine-in' || orderState.tableId) &&
           !isProcessing;
  }, [orderState.items.length, orderState.orderType, orderState.tableId, isProcessing]);

  // Handlers
  const handleViewChange = useCallback((view: MobileUiState['currentView']) => {
    setUiState(prev => ({ ...prev, currentView: view }));
  }, []);

  const handleAddItem = useCallback((item: MenuItem, size: string, addons: OrderAddon[], notes: string, categoryId: string) => {
    dispatch({
      type: 'ADD_ITEM',
      payload: { item, size, addons, notes, categoryId }
    });
    
    toast({
      title: "Article ajouté",
      description: `${item.name} ajouté à la commande`,
    });
  }, [toast]);

  const handleIncrementItem = useCallback((itemId: string) => {
    dispatch({ type: 'INCREMENT_QUANTITY', payload: { itemId } });
  }, []);

  const handleDecrementItem = useCallback((itemId: string) => {
    dispatch({ type: 'DECREMENT_QUANTITY', payload: { itemId } });
  }, []);

  const handleRemoveItem = useCallback((itemId: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: { itemId } });
  }, []);

  const handleItemCustomization = useCallback((item: MenuItem, categoryId: string) => {
    setSelectedItemForCustomization({ item, categoryId });
    setUiState(prev => ({ ...prev, currentView: 'customization' }));
  }, []);

  const handleCustomizationConfirm = useCallback((size: string, addons: OrderAddon[], notes: string) => {
    if (selectedItemForCustomization) {
      handleAddItem(selectedItemForCustomization.item, size, addons, notes, selectedItemForCustomization.categoryId);
      setSelectedItemForCustomization(null);
      setUiState(prev => ({ ...prev, currentView: 'menu' }));
    }
  }, [selectedItemForCustomization, handleAddItem]);

  const handleCustomizationCancel = useCallback(() => {
    setSelectedItemForCustomization(null);
    setUiState(prev => ({ ...prev, currentView: 'menu' }));
  }, []);

  const handlePlaceOrder = useCallback(async () => {
    if (!canPlaceOrder) return;

    setIsProcessing(true);
    try {
      // Combine items with same signature
      const combinedItemsMap: { [key: string]: OrderItem } = {};

      orderState.items.forEach((item: OrderItem) => {
        const signature = `${item.menuItemId}-${item.size}-${JSON.stringify(item.addons)}-${item.notes}`;
        if (combinedItemsMap[signature]) {
          combinedItemsMap[signature].quantity += item.quantity;
        } else {
          combinedItemsMap[signature] = { ...item };
        }
      });

      const combinedItems = Object.values(combinedItemsMap);

      // Create new order object
      const newOrder: NewOrder = {
        tableId: orderState.orderType === 'dine-in' ? orderState.tableId : '',
        orderType: orderState.orderType,
        status: 'pending',
        items: combinedItems.map((item: OrderItem) => ({ ...item, notes: item.notes || '' })),
        total: calculateTotal(combinedItems),
        notes: orderState.notes,
        customer: (orderState.orderType !== 'dine-in' && orderState.customer)
          ? orderState.customer
          : undefined,
        deliveryPerson: orderState.orderType === 'delivery'
          ? orderState.deliveryPerson
          : undefined,
        paymentStatus: 'unpaid',
        createdBy: user?.name || (user as any)?.username || 'unknown',
        createdByName: user?.name || 'Personnel Inconnu'
      };

      await createOrder(newOrder);

      toast({
        title: "Commande créée",
        description: `Commande ${orderState.orderType === 'dine-in' ? 'pour table ' + tables.find(t => t.id === orderState.tableId)?.name : 'créée'} avec succès`,
      });

      // Reset order
      dispatch({ type: 'CLEAR_ORDER' });
      setUiState(prev => ({ ...prev, currentView: 'menu' }));

    } catch (error) {
      console.error('Error creating order:', error);
      toast({
        title: "Erreur",
        description: "Impossible de créer la commande. Veuillez réessayer.",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  }, [canPlaceOrder, orderState, createOrder, user, toast, tables]);

  // Loading states
  if (!menuReady || !tablesReady || !ordersReady) {
    return (
      <MobileContainer className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Chargement...</p>
        </div>
      </MobileContainer>
    );
  }

  return (
    <div className={cn(
      "flex flex-col h-screen bg-background mobile-waiter-interface",
      !isMobile && "max-w-md mx-auto border-x" // Desktop testing: constrain width and add borders
    )}>
      {/* Mobile Header */}
      <div
        className="flex-shrink-0 bg-background border-b px-4 py-3 flex items-center justify-between mobile-header mobile-backdrop"
        style={{ paddingTop: `max(${safeAreaInsets.top}px, 12px)` }}
      >
        <div className="flex items-center gap-3">
          <h1 className="text-lg font-semibold">Serveur</h1>
          {process.env.NODE_ENV === 'development' && !isMobile && (
            <div className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
              📱 Mode Mobile (Test)
            </div>
          )}
          {orderState.orderType === 'dine-in' && orderState.tableId && (
            <div className="text-sm text-muted-foreground bg-muted px-2 py-1 rounded">
              Table {tables.find(t => t.id === orderState.tableId)?.name || orderState.tableId}
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {totalItemCount > 0 && (
            <div className="text-sm font-medium text-primary">
              {totalItemCount} article{totalItemCount > 1 ? 's' : ''}
            </div>
          )}
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 overflow-hidden mobile-container">
        {uiState.currentView === 'menu' && (
          <MobileMenuBrowser
            categories={categories}
            onAddItem={handleAddItem}
            onItemCustomize={handleItemCustomization}
            selectedCategory={uiState.selectedCategory}
            onCategoryChange={(categoryId) => {
              setUiState(prev => ({ ...prev, selectedCategory: categoryId }));
            }}
          />
        )}

        {uiState.currentView === 'cart' && (
          <MobileCart
            items={orderState.items}
            total={orderState.total}
            onIncrement={handleIncrementItem}
            onDecrement={handleDecrementItem}
            onRemove={handleRemoveItem}
            onItemEdit={(itemId) => {
              // Handle item editing - could open customization for existing item
            }}
            onCheckout={() => handleViewChange('order-flow')}
          />
        )}

        {uiState.currentView === 'customization' && selectedItemForCustomization && (
          <MobileItemCustomization
            item={selectedItemForCustomization.item}
            categoryId={selectedItemForCustomization.categoryId}
            categoryColor="#3b82f6" // You can make this dynamic based on category
            onConfirm={handleCustomizationConfirm}
            onCancel={handleCustomizationCancel}
          />
        )}

        {uiState.currentView === 'order-flow' && (
          <MobileOrderFlow
            items={orderState.items}
            total={orderState.total}
            orderType={orderState.orderType}
            tableId={orderState.tableId}
            tables={tables}
            customer={orderState.customer}
            notes={orderState.notes}
            onOrderTypeChange={(orderType) => dispatch({ type: 'SET_ORDER_TYPE', payload: { orderType } })}
            onTableChange={(tableId) => dispatch({ type: 'SET_TABLE', payload: { tableId } })}
            onCustomerChange={(customer) => dispatch({ type: 'SET_CUSTOMER', payload: { customer } })}
            onNotesChange={(notes) => dispatch({ type: 'SET_NOTES', payload: { notes } })}
            onPlaceOrder={handlePlaceOrder}
            onCancel={() => handleViewChange('cart')}
            isProcessing={isProcessing}
          />
        )}
      </div>

      {/* Mobile Bottom Navigation */}
      <MobileBottomNav className="flex-shrink-0 mobile-bottom-nav">
        <div className="flex items-center justify-around">
          <button
            onClick={() => handleViewChange('menu')}
            className={cn(
              "flex flex-col items-center gap-1 p-2 rounded-lg transition-colors mobile-button touch-manipulation",
              uiState.currentView === 'menu' ? "text-primary bg-primary/10" : "text-muted-foreground"
            )}
          >
            <MenuIcon className="h-5 w-5" />
            <span className="text-xs">Menu</span>
          </button>
          
          <button
            onClick={() => handleViewChange('cart')}
            className={cn(
              "flex flex-col items-center gap-1 p-2 rounded-lg transition-colors relative mobile-button touch-manipulation",
              uiState.currentView === 'cart' ? "text-primary bg-primary/10" : "text-muted-foreground"
            )}
          >
            <ShoppingCart className="h-5 w-5" />
            <span className="text-xs">Panier</span>
            {totalItemCount > 0 && (
              <div className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center mobile-animate">
                {totalItemCount}
              </div>
            )}
          </button>

          <button
            onClick={() => handleViewChange('order-flow')}
            disabled={!canPlaceOrder}
            className={cn(
              "flex flex-col items-center gap-1 p-2 rounded-lg transition-colors mobile-button touch-manipulation",
              canPlaceOrder ? "text-primary bg-primary/10" : "text-muted-foreground opacity-50"
            )}
          >
            <Receipt className="h-5 w-5" />
            <span className="text-xs">Commander</span>
          </button>
        </div>
      </MobileBottomNav>
    </div>
  );
});

export default MobileWaiterInterface;
