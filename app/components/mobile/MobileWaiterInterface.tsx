"use client";

import { useState, useEffect, use<PERSON>allback, useMemo } from "react";
import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { useTableDB } from "@/lib/hooks/useTableDB";
import { useMenuV4 } from "@/lib/hooks/use-menu-v4";
import { useOrderV4 } from "@/lib/hooks/use-order-v4";
import { cn } from "@/lib/utils";

// UI Components
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";

// Icons
import { 
  Search, 
  ShoppingCart, 
  Plus, 
  Minus, 
  Table as TableIcon,
  X
} from "lucide-react";

// Types and interfaces
import { OrderItem, Order } from "@/lib/types";
import type { MenuItem, Addon, Category } from "@/lib/db/v4-menu-service";
import { TableLayout as Table } from "@/lib/db/table-db";
import { useToast } from "@/components/ui/use-toast";

export default function MobileWaiterInterface() {
  // Auth and database hooks
  const { user } = useAuth();
  const { tables, isLoading: tablesLoading, error: tablesError, isReady: tablesReady } = useTableDB();
  const { categories, isLoading: menuLoading, error: menuError, isReady: menuReady } = useMenuV4();
  const { createOrder, isLoading: ordersLoading, error: ordersError, isReady: ordersReady } = useOrderV4();
  const { toast } = useToast();

  // UI State
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isTableDialogOpen, setIsTableDialogOpen] = useState(false);
  const [selectedItemForAddons, setSelectedItemForAddons] = useState<MenuItem | null>(null);
  const [selectedAddons, setSelectedAddons] = useState<{[key: string]: Addon[]}>({});
  const [itemNotes, setItemNotes] = useState<{[key: string]: string}>({});
  const [isOperationLoading, setIsOperationLoading] = useState(false);

  // Order State
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [selectedItemSizes, setSelectedItemSizes] = useState<{[key: string]: string}>({});
  const [currentOrder, setCurrentOrder] = useState<Order>({
    id: "",
    type: "table",
    status: "pending",
    tableId: "",
    items: [],
    total: 0,
    createdAt: new Date()
  });

  // Set initial category when categories load
  useEffect(() => {
    if (categories.length > 0 && !selectedCategory) {
      setSelectedCategory(categories[0].id);
    }
  }, [categories, selectedCategory]);

  // Calculate total price for an item including addons
  const calculateItemPrice = useCallback((item: MenuItem, size: string, addons: Addon[] = []) => {
    const basePrice = item.prices[size] || 0;
    const addonPrice = addons.reduce((sum, addon) => sum + (addon.price || 0), 0);
    return basePrice + addonPrice;
  }, []);

  // Calculate total for all items in order
  const calculateTotal = useCallback((items: OrderItem[]) => {
    return items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  }, []);

  // Add item to order
  const addItemToOrder = useCallback((item: MenuItem, size: string, addons: Addon[] = [], notes: string = "") => {
    const price = calculateItemPrice(item, size, addons);
    
    // Check if identical item already exists
    const existingItemIndex = currentOrder.items.findIndex(orderItem => 
      orderItem.menuItemId === item.id &&
      orderItem.size === size &&
      JSON.stringify(orderItem.addons) === JSON.stringify(addons) &&
      orderItem.notes === notes
    );

    if (existingItemIndex !== -1) {
      // Increment quantity of existing item
      const updatedItems = [...currentOrder.items];
      updatedItems[existingItemIndex] = {
        ...updatedItems[existingItemIndex],
        quantity: updatedItems[existingItemIndex].quantity + 1
      };

      setCurrentOrder(prev => ({
        ...prev,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      }));
    } else {
      // Add new item
      const newItem: OrderItem = {
        id: `${Date.now()}-${Math.random()}`,
        menuItemId: item.id,
        name: item.name,
        price: price,
        quantity: 1,
        size: size,
        addons: addons.map(addon => ({
          id: addon.id,
          name: addon.name,
          price: addon.price || 0
        })),
        notes: notes
      };

      const updatedItems = [...currentOrder.items, newItem];
      setCurrentOrder(prev => ({
        ...prev,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      }));
    }
  }, [currentOrder.items, calculateItemPrice, calculateTotal]);

  // Handle quick add (for items without customization)
  const handleQuickAdd = useCallback((item: MenuItem) => {
    const defaultSize = Object.keys(item.prices)[0] || 'default';
    addItemToOrder(item, defaultSize, [], "");
  }, [addItemToOrder]);

  // Handle item customization
  const handleItemCustomization = useCallback((item: MenuItem) => {
    setSelectedItemForAddons(item);
    setSelectedItemSizes(prev => ({
      ...prev,
      [item.id]: Object.keys(item.prices)[0] || 'default'
    }));
  }, []);

  // Remove item from order
  const removeItemFromOrder = useCallback((itemId: string) => {
    const updatedItems = currentOrder.items.filter(item => item.id !== itemId);
    setCurrentOrder(prev => ({
      ...prev,
      items: updatedItems,
      total: calculateTotal(updatedItems)
    }));
  }, [currentOrder.items, calculateTotal]);

  // Update item quantity
  const updateItemQuantity = useCallback((itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItemFromOrder(itemId);
      return;
    }

    const updatedItems = currentOrder.items.map(item =>
      item.id === itemId ? { ...item, quantity: newQuantity } : item
    );

    setCurrentOrder(prev => ({
      ...prev,
      items: updatedItems,
      total: calculateTotal(updatedItems)
    }));
  }, [currentOrder.items, calculateTotal, removeItemFromOrder]);

  // Place order
  const handlePlaceOrder = useCallback(async () => {
    if (!selectedTable || currentOrder.items.length === 0) {
      toast({
        title: "Error",
        description: "Please select a table and add items to the order.",
        variant: "destructive"
      });
      return;
    }

    setIsOperationLoading(true);

    try {
      const newOrder = {
        orderType: "dine-in" as const,
        status: "pending" as const,
        tableId: selectedTable.id,
        items: currentOrder.items,
        total: currentOrder.total,
        notes: ""
      };

      await createOrder(newOrder);

      toast({
        title: "Order Placed",
        description: `Order for table ${selectedTable.name} has been placed successfully.`,
      });

      // Reset order
      setCurrentOrder({
        id: "",
        type: "table",
        status: "pending",
        tableId: "",
        items: [],
        total: 0,
        createdAt: new Date()
      });
      setSelectedTable(null);
      setSelectedItemForAddons(null);
      setSelectedAddons({});
      setItemNotes({});
      setSelectedItemSizes({});
      setIsCartOpen(false);

    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to place order. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsOperationLoading(false);
    }
  }, [selectedTable, currentOrder, createOrder, toast]);

  // Filter menu items based on search and category
  const filteredMenuItems = useMemo(() => {
    const category = categories.find(c => c.id === selectedCategory);
    if (!category) return [];

    return category.items.filter(item =>
      item.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [categories, selectedCategory, searchQuery]);

  // Loading state
  if (!tablesReady || !menuReady || !ordersReady) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Header */}
      <div className="flex-shrink-0 p-3 border-b bg-background">
        <div className="flex items-center gap-2 mb-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsTableDialogOpen(true)}
            className="h-8"
          >
            <TableIcon className="h-4 w-4 mr-1" />
            {selectedTable ? `Table ${selectedTable.name}` : 'Select Table'}
          </Button>
          <Sheet open={isCartOpen} onOpenChange={setIsCartOpen}>
            <SheetTrigger asChild>
              <Button size="sm" className="h-8 relative">
                <ShoppingCart className="h-4 w-4 mr-1" />
                Cart
                {currentOrder.items.length > 0 && (
                  <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                    {currentOrder.items.reduce((sum, item) => sum + item.quantity, 0)}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-full sm:max-w-md">
              <div className="flex flex-col h-full">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold">Cart</h2>
                  <Badge variant="secondary">{currentOrder.items.length} items</Badge>
                </div>

                {currentOrder.items.length === 0 ? (
                  <div className="flex-1 flex items-center justify-center">
                    <div className="text-center">
                      <ShoppingCart className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">Your cart is empty</p>
                    </div>
                  </div>
                ) : (
                  <>
                    <ScrollArea className="flex-1">
                      <div className="space-y-3">
                        {currentOrder.items.map((item) => (
                          <div key={item.id} className="flex items-center gap-3 p-3 border rounded-lg">
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-sm truncate">{item.name}</h3>
                              <p className="text-xs text-muted-foreground">
                                {item.size && `Size: ${item.size}`}
                                {item.addons && item.addons.length > 0 && (
                                  <span className="block">
                                    Addons: {item.addons.map(addon => addon.name).join(', ')}
                                  </span>
                                )}
                              </p>
                              <p className="text-sm font-medium">{item.price} DA</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => updateItemQuantity(item.id, item.quantity - 1)}
                                className="h-8 w-8 p-0"
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <span className="text-sm font-medium w-8 text-center">{item.quantity}</span>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => updateItemQuantity(item.id, item.quantity + 1)}
                                className="h-8 w-8 p-0"
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>

                    <div className="border-t pt-4 mt-4">
                      <div className="flex justify-between items-center mb-4">
                        <span className="font-semibold">Total:</span>
                        <span className="font-semibold text-lg">{currentOrder.total} DA</span>
                      </div>
                      <Button
                        onClick={handlePlaceOrder}
                        disabled={!selectedTable || isOperationLoading}
                        className="w-full"
                      >
                        {isOperationLoading ? "Placing Order..." : "Place Order"}
                      </Button>
                    </div>
                  </>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search menu items..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-8"
          />
        </div>
      </div>

      {/* Categories */}
      <div className="flex-shrink-0 p-3 border-b">
        <ScrollArea className="w-full">
          <div className="flex gap-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="whitespace-nowrap h-8"
              >
                {category.name}
              </Button>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Menu Items */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-3 space-y-2">
            {filteredMenuItems.map((item) => {
              const hasMultipleSizes = Object.keys(item.prices).length > 1;
              const hasAddons = item.addons && item.addons.length > 0;
              const needsCustomization = hasMultipleSizes || hasAddons;
              const defaultSize = Object.keys(item.prices)[0] || 'default';
              const defaultPrice = item.prices[defaultSize] || 0;

              return (
                <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-sm truncate">{item.name}</h3>
                    <p className="text-xs text-muted-foreground mt-1">{defaultPrice} DA</p>
                    {item.description && (
                      <p className="text-xs text-muted-foreground mt-1 line-clamp-2">{item.description}</p>
                    )}
                  </div>
                  <div className="flex-shrink-0 ml-3">
                    {needsCustomization ? (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleItemCustomization(item)}
                        className="h-8 w-8 p-0"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    ) : (
                      <Button
                        size="sm"
                        onClick={() => handleQuickAdd(item)}
                        className="h-8 w-8 p-0"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </ScrollArea>
      </div>

      {/* Table Selection Dialog */}
      <Dialog open={isTableDialogOpen} onOpenChange={setIsTableDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <div className="space-y-4">
            <h2 className="text-lg font-semibold">Select Table</h2>
            <ScrollArea className="max-h-60">
              <div className="grid grid-cols-2 gap-2">
                {tables.map((table) => (
                  <Button
                    key={table.id}
                    variant={selectedTable?.id === table.id ? "default" : "outline"}
                    onClick={() => {
                      setSelectedTable(table);
                      setCurrentOrder(prev => ({ ...prev, tableId: table.id }));
                      setIsTableDialogOpen(false);
                    }}
                    className="h-12"
                  >
                    Table {table.name}
                  </Button>
                ))}
              </div>
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>

      {/* Item Customization Dialog */}
      {selectedItemForAddons && (
        <Dialog open={!!selectedItemForAddons} onOpenChange={() => setSelectedItemForAddons(null)}>
          <DialogContent className="sm:max-w-md">
            <div className="space-y-4">
              <h2 className="text-lg font-semibold">{selectedItemForAddons.name}</h2>

              {/* Size Selection */}
              {Object.keys(selectedItemForAddons.prices).length > 1 && (
                <div>
                  <h3 className="font-medium mb-2">Size</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {Object.entries(selectedItemForAddons.prices).map(([size, price]) => (
                      <Button
                        key={size}
                        variant={selectedItemSizes[selectedItemForAddons.id] === size ? "default" : "outline"}
                        onClick={() => setSelectedItemSizes(prev => ({ ...prev, [selectedItemForAddons.id]: size }))}
                        className="h-12 flex flex-col"
                      >
                        <span className="font-medium">{size}</span>
                        <span className="text-xs">{price} DA</span>
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              {/* Addons */}
              {selectedItemForAddons.addons && selectedItemForAddons.addons.length > 0 && (
                <div>
                  <h3 className="font-medium mb-2">Addons</h3>
                  <div className="space-y-2">
                    {selectedItemForAddons.addons.map((addon) => (
                      <div key={addon.id} className="flex items-center justify-between p-2 border rounded">
                        <div>
                          <span className="text-sm font-medium">{addon.name}</span>
                          <span className="text-xs text-muted-foreground ml-2">+{addon.price || 0} DA</span>
                        </div>
                        <Button
                          size="sm"
                          variant={selectedAddons[selectedItemForAddons.id]?.some(a => a.id === addon.id) ? "default" : "outline"}
                          onClick={() => {
                            const currentAddons = selectedAddons[selectedItemForAddons.id] || [];
                            const isSelected = currentAddons.some(a => a.id === addon.id);

                            if (isSelected) {
                              setSelectedAddons(prev => ({
                                ...prev,
                                [selectedItemForAddons.id]: currentAddons.filter(a => a.id !== addon.id)
                              }));
                            } else {
                              setSelectedAddons(prev => ({
                                ...prev,
                                [selectedItemForAddons.id]: [...currentAddons, addon]
                              }));
                            }
                          }}
                          className="h-8 w-8 p-0"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Notes */}
              <div>
                <h3 className="font-medium mb-2">Notes</h3>
                <Input
                  placeholder="Special instructions..."
                  value={itemNotes[selectedItemForAddons.id] || ""}
                  onChange={(e) => setItemNotes(prev => ({ ...prev, [selectedItemForAddons.id]: e.target.value }))}
                />
              </div>

              {/* Add to Cart Button */}
              <Button
                onClick={() => {
                  const size = selectedItemSizes[selectedItemForAddons.id] || Object.keys(selectedItemForAddons.prices)[0];
                  const addons = selectedAddons[selectedItemForAddons.id] || [];
                  const notes = itemNotes[selectedItemForAddons.id] || "";

                  addItemToOrder(selectedItemForAddons, size, addons, notes);
                  setSelectedItemForAddons(null);
                }}
                className="w-full"
              >
                Add to Cart
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
