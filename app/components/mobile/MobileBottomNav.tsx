"use client";

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ShoppingCart, 
  Menu, 
  Clock, 
  User,
  Home
} from 'lucide-react';

interface MobileBottomNavProps {
  activeTab: 'menu' | 'cart' | 'orders' | 'profile';
  onTabChange: (tab: 'menu' | 'cart' | 'orders' | 'profile') => void;
  cartItemCount?: number;
  className?: string;
}

export function MobileBottomNav({ 
  activeTab, 
  onTabChange, 
  cartItemCount = 0,
  className 
}: MobileBottomNavProps) {
  const tabs = [
    {
      id: 'menu' as const,
      label: 'Menu',
      icon: Menu,
      badge: null
    },
    {
      id: 'cart' as const,
      label: 'Panier',
      icon: ShoppingCart,
      badge: cartItemCount > 0 ? cartItemCount : null
    },
    {
      id: 'orders' as const,
      label: 'Commandes',
      icon: Clock,
      badge: null
    },
    {
      id: 'profile' as const,
      label: 'Profil',
      icon: User,
      badge: null
    }
  ];

  return (
    <div className={cn(
      'fixed bottom-0 left-0 right-0 z-50',
      'bg-background border-t border-border',
      'px-2 py-2 safe-area-pb',
      className
    )}>
      <div className="flex items-center justify-around max-w-md mx-auto">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          const isActive = activeTab === tab.id;
          
          return (
            <Button
              key={tab.id}
              variant="ghost"
              size="sm"
              onClick={() => onTabChange(tab.id)}
              className={cn(
                'flex flex-col items-center gap-1 h-auto py-2 px-3',
                'text-xs font-medium relative',
                isActive 
                  ? 'text-primary bg-primary/10' 
                  : 'text-muted-foreground hover:text-foreground'
              )}
            >
              <div className="relative">
                <Icon className="h-5 w-5" />
                {tab.badge && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs flex items-center justify-center"
                  >
                    {tab.badge}
                  </Badge>
                )}
              </div>
              <span className="leading-none">{tab.label}</span>
            </Button>
          );
        })}
      </div>
    </div>
  );
}
