"use client";

import { cn } from '@/lib/utils';

interface MobileContainerProps {
  children: React.ReactNode;
  className?: string;
  withPadding?: boolean;
  withBottomNav?: boolean;
}

export function MobileContainer({ 
  children, 
  className, 
  withPadding = true,
  withBottomNav = false 
}: MobileContainerProps) {
  return (
    <div 
      className={cn(
        'min-h-screen bg-background',
        withPadding && 'px-4',
        withBottomNav && 'pb-20', // Space for bottom navigation
        className
      )}
    >
      {children}
    </div>
  );
}
